import { FastifyInstance } from 'fastify';
import os from 'os';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Interface para métricas de saúde
interface HealthMetrics {
  status: 'healthy' | 'warning' | 'critical';
  timestamp: string;
  uptime: number;
  memory: {
    rss: string;
    heapUsed: string;
    heapTotal: string;
    external: string;
    arrayBuffers: string;
    free: string;
    total: string;
    usage: number;
  };
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  connections: {
    active: number;
    limit: number;
  };
  processes: {
    node: number;
    ssh: number;
    zombie: number;
  };
  disk: {
    free: string;
    total: string;
    usage: string;
  };
  eventEmitters: {
    maxListeners: number;
    defaultMaxListeners: number;
  };
  alerts: string[];
}

// Função para obter uso de CPU
async function getCpuUsage(): Promise<number> {
  try {
    const { stdout } = await execAsync("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | sed 's/%us,//'");
    return parseFloat(stdout.trim()) || 0;
  } catch {
    return 0;
  }
}

// Função para obter informações de disco
async function getDiskInfo(): Promise<{ free: string; total: string; usage: string }> {
  try {
    const { stdout } = await execAsync("df -h / | awk 'NR==2 {print $2 \"|\" $3 \"|\" $4}'");
    const [total, used, free] = stdout.trim().split('|');
    return { free, total, usage: used };
  } catch {
    return { free: 'N/A', total: 'N/A', usage: 'N/A' };
  }
}

// Função para contar processos
async function getProcessCounts(): Promise<{ node: number; ssh: number; zombie: number }> {
  try {
    const [nodeResult, sshResult, zombieResult] = await Promise.all([
      execAsync("ps aux | grep node | grep -v grep | wc -l").catch(() => ({ stdout: '0' })),
      execAsync("ps aux | grep ssh | grep -v grep | wc -l").catch(() => ({ stdout: '0' })),
      execAsync("ps aux | grep -w Z | wc -l").catch(() => ({ stdout: '0' }))
    ]);

    return {
      node: parseInt(nodeResult.stdout.trim()) || 0,
      ssh: parseInt(sshResult.stdout.trim()) || 0,
      zombie: parseInt(zombieResult.stdout.trim()) || 0
    };
  } catch {
    return { node: 0, ssh: 0, zombie: 0 };
  }
}

export async function healthRoutes(app: FastifyInstance) {
  // Endpoint de saúde básico (compatibilidade)
  app.get('/health', async (request, reply) => {
    return reply.send({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });

  // Endpoint de saúde detalhado
  app.get('/health/detailed', async (request, reply) => {
  try {
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    const memoryUsagePercent = Math.round((usedMem / totalMem) * 100);

    const [cpuUsage, diskInfo, processCounts] = await Promise.all([
      getCpuUsage(),
      getDiskInfo(),
      getProcessCounts()
    ]);

    // Simular contagem de conexões ativas (você pode implementar um contador real)
    const activeConnections = 0; // TODO: Implementar contador real de conexões SSH ativas

    // Gerar alertas baseados nas métricas
    const alerts: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (memoryUsagePercent > 85) {
      alerts.push(`Uso de memória crítico: ${memoryUsagePercent}%`);
      status = 'critical';
    } else if (memoryUsagePercent > 70) {
      alerts.push(`Uso de memória alto: ${memoryUsagePercent}%`);
      if (status === 'healthy') status = 'warning';
    }

    if (processCounts.ssh > 20) {
      alerts.push(`Muitas conexões SSH: ${processCounts.ssh}`);
      if (status === 'healthy') status = 'warning';
    }

    if (processCounts.node > 3) {
      alerts.push(`Muitos processos Node.js: ${processCounts.node}`);
      if (status === 'healthy') status = 'warning';
    }

    if (processCounts.zombie > 0) {
      alerts.push(`Processos zumbis detectados: ${processCounts.zombie}`);
      if (status === 'healthy') status = 'warning';
    }

    if (cpuUsage > 90) {
      alerts.push(`Uso de CPU crítico: ${cpuUsage}%`);
      status = 'critical';
    }

    const metrics: HealthMetrics = {
      status,
      timestamp: new Date().toISOString(),
      uptime: Math.round(process.uptime()),
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024) + 'MB',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
        external: Math.round(memUsage.external / 1024 / 1024) + 'MB',
        arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024) + 'MB',
        free: Math.round(freeMem / 1024 / 1024) + 'MB',
        total: Math.round(totalMem / 1024 / 1024) + 'MB',
        usage: memoryUsagePercent
      },
      cpu: {
        usage: Math.round(cpuUsage),
        loadAverage: os.loadavg().map(load => Math.round(load * 100) / 100)
      },
      connections: {
        active: activeConnections,
        limit: 20
      },
      processes: processCounts,
      disk: diskInfo,
      eventEmitters: {
        maxListeners: process.getMaxListeners(),
        defaultMaxListeners: require('events').EventEmitter.defaultMaxListeners
      },
      alerts
    };

    return reply.send(metrics);
  } catch (error) {
    console.error('Erro ao obter métricas de saúde:', error);
    return reply.status(500).send({
      status: 'error',
      message: 'Erro interno ao obter métricas de saúde',
      timestamp: new Date().toISOString()
    });
  }
  });

  // Endpoint para forçar limpeza de recursos
  app.post('/health/cleanup', async (request, reply) => {
  try {
    console.log('Executando limpeza manual de recursos...');
    
    // Executar comandos de limpeza
    const cleanupCommands = [
      "find /tmp -name 'ssh_*' -mmin +30 -delete",
      "find /tmp -name '.ssh_*' -mmin +30 -delete", 
      "find /tmp -name '*.lock' -mmin +60 -delete"
    ];

    for (const command of cleanupCommands) {
      try {
        await execAsync(command);
      } catch (error) {
        console.warn(`Comando de limpeza falhou: ${command}`, error);
      }
    }

    // Forçar garbage collection
    if (global.gc) {
      global.gc();
    }

    return reply.send({
      status: 'success',
      message: 'Limpeza de recursos executada com sucesso',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Erro durante limpeza:', error);
    return reply.status(500).send({
      status: 'error',
      message: 'Erro durante limpeza de recursos',
      timestamp: new Date().toISOString()
    });
  }
  });

  // Endpoint para obter histórico de memory leaks
  app.get('/health/memory-leaks', async (request, reply) => {
  try {
    const { stdout } = await execAsync("grep -c 'MaxListenersExceeded' /var/log/*.log 2>/dev/null || echo '0'");
    const leakCount = parseInt(stdout.trim()) || 0;

    return reply.send({
      memoryLeaks: {
        detected: leakCount,
        lastCheck: new Date().toISOString(),
        status: leakCount > 0 ? 'warning' : 'healthy'
      }
    });
  } catch (error) {
    return reply.status(500).send({
      status: 'error',
      message: 'Erro ao verificar memory leaks'
    });
  }
  });
}
